require('dotenv').config();
const express = require('express');
const cors = require('cors');
const path = require('path');

// Import utility modules
const { readCodeFilesRecursively, chunkContent, SUPPORTED_EXTENSIONS } = require('./fileReader');
const { initializeOpenAI, createEmbedding, createEmbeddingsForChunks, generateAIResponse } = require('./embeddings');
const { findSimilarChunks } = require('./similarity');

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 3002;

// Middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://127.0.0.1:3000', 'http://localhost:5500', 'http://127.0.0.1:5500', 'http://localhost:8080', 'http://127.0.0.1:8080'],
  credentials: true
}));
app.use(express.json({ limit: '50mb' }));

// In-memory storage for embeddings
let embeddingsStore = [];

// Initialize AI Services
try {
  if (process.env.MOCK_MODE === 'true') {
    console.log('🎭 Mock mode enabled - AI API calls will be simulated');
    console.log('✅ Mock mode initialized successfully');
  } else if (process.env.HUGGINGFACE_API_KEY) {
    console.log('🤗 Hugging Face API key detected');
    console.log('✅ Hugging Face client initialized successfully');
  } else if (process.env.OPENAI_API_KEY) {
    initializeOpenAI(process.env.OPENAI_API_KEY);
    console.log('✅ OpenAI client initialized successfully');
  } else {
    console.error('ERROR: No AI API key found');
    console.error('Please add HUGGINGFACE_API_KEY or OPENAI_API_KEY to your .env file');
    process.exit(1);
  }
} catch (error) {
  console.error('❌ Failed to initialize AI client:', error.message);
  process.exit(1);
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    embeddings: {
      totalChunks: embeddingsStore.length,
      validEmbeddings: embeddingsStore.filter(chunk => chunk.embedding !== null).length
    }
  });
});

// GET /status - Get current embeddings status
app.get('/status', (req, res) => {
  const validChunks = embeddingsStore.filter(chunk => chunk.embedding !== null);
  const fileStats = {};
  
  embeddingsStore.forEach(chunk => {
    const ext = chunk.extension;
    if (!fileStats[ext]) {
      fileStats[ext] = { files: new Set(), chunks: 0 };
    }
    fileStats[ext].files.add(chunk.path);
    fileStats[ext].chunks++;
  });

  // Convert sets to counts
  Object.keys(fileStats).forEach(ext => {
    fileStats[ext].files = fileStats[ext].files.size;
  });

  res.json({
    isReady: validChunks.length > 0,
    statistics: {
      totalChunks: embeddingsStore.length,
      validEmbeddings: validChunks.length,
      failedEmbeddings: embeddingsStore.length - validChunks.length,
      supportedExtensions: SUPPORTED_EXTENSIONS,
      fileStatistics: fileStats
    }
  });
});

// POST /ingest-files - Process files sent from frontend and create embeddings
app.post('/ingest-files', async (req, res) => {
  try {
    const { projectName, files } = req.body;

    // Validation
    if (!projectName || !files || !Array.isArray(files)) {
      return res.status(400).json({
        error: 'Invalid request',
        message: 'Please provide projectName and files array in the request body'
      });
    }

    if (files.length === 0) {
      return res.status(400).json({
        error: 'No files provided',
        message: 'The files array cannot be empty'
      });
    }

    console.log(`🔍 Starting ingestion for project: ${projectName} (${files.length} files)`);

    // Convert files to chunks format expected by the embedding system
    const chunks = [];
    for (const file of files) {
      if (!file.path || !file.content) {
        console.warn(`Skipping invalid file:`, file);
        continue;
      }

      // Use the existing chunking logic from fileReader
      const fileChunks = chunkContent(file.content, file.path);
      chunks.push(...fileChunks);
    }

    if (chunks.length === 0) {
      return res.status(400).json({
        error: 'No valid chunks created',
        message: 'No valid content could be extracted from the provided files'
      });
    }

    console.log(`📄 Created ${chunks.length} code chunks from ${files.length} files`);

    // Create embeddings for all chunks
    const startTime = Date.now();
    console.log('🧠 Creating embeddings...');
    
    const chunksWithEmbeddings = await createEmbeddingsForChunks(chunks, (current, total) => {
      if (current % 5 === 0 || current === total) {
        console.log(`⏳ Progress: ${current}/${total} embeddings created`);
      }
    });

    const endTime = Date.now();
    const processingTime = (endTime - startTime) / 1000;

    // Store embeddings in memory
    embeddingsStore = chunksWithEmbeddings;

    const validEmbeddings = chunksWithEmbeddings.filter(chunk => chunk.embedding !== null).length;
    const totalFiles = files.length;

    console.log(`✅ Ingestion completed: ${validEmbeddings}/${chunks.length} embeddings created in ${processingTime}s`);

    res.json({
      success: true,
      message: 'Project ingestion completed successfully',
      statistics: {
        totalChunks: chunks.length,
        successfulEmbeddings: validEmbeddings,
        failedEmbeddings: chunks.length - validEmbeddings,
        totalFiles: totalFiles,
        processingTimeSeconds: Math.round(processingTime * 100) / 100
      }
    });

  } catch (error) {
    console.error('❌ Ingestion error:', error);
    res.status(500).json({
      error: 'Internal server error during ingestion',
      message: error.message
    });
  }
});

// POST /query - Answer user questions using embeddings
app.post('/query', async (req, res) => {
  try {
    const { question } = req.body;

    // Validation
    if (!question) {
      return res.status(400).json({
        error: 'Question is required',
        message: 'Please provide a question in the request body'
      });
    }

    if (embeddingsStore.length === 0) {
      return res.status(400).json({
        error: 'No embeddings available',
        message: 'Please ingest a project first using the /ingest-files endpoint'
      });
    }

    const validChunks = embeddingsStore.filter(chunk => chunk.embedding !== null);
    if (validChunks.length === 0) {
      return res.status(400).json({
        error: 'No valid embeddings available',
        message: 'All embeddings failed during ingestion. Please try ingesting the project again.'
      });
    }

    console.log(`❓ Processing query: "${question}"`);

    // Create embedding for the question
    let questionEmbedding;
    try {
      questionEmbedding = await createEmbedding(question);
    } catch (error) {
      return res.status(500).json({
        error: 'Failed to create question embedding',
        message: error.message
      });
    }

    // Find top 3 most similar chunks
    const similarChunks = findSimilarChunks(questionEmbedding, validChunks, 3);

    if (similarChunks.length === 0) {
      return res.status(500).json({
        error: 'No similar chunks found',
        message: 'Unable to find relevant code chunks for the question'
      });
    }

    console.log(`🔍 Found ${similarChunks.length} relevant chunks`);

    // Generate AI response
    let aiResponse;
    try {
      aiResponse = await generateAIResponse(question, similarChunks);
    } catch (error) {
      return res.status(500).json({
        error: 'Failed to generate AI response',
        message: error.message
      });
    }

    console.log(`✅ Query completed successfully`);

    res.json({
      success: true,
      question: question,
      answer: aiResponse,
      context: similarChunks.map(chunk => ({
        path: chunk.path,
        chunkIndex: chunk.chunkIndex,
        totalChunks: chunk.totalChunks,
        similarityScore: Math.round(chunk.similarityScore * 1000) / 1000,
        tokens: chunk.tokens
      }))
    });

  } catch (error) {
    console.error('❌ Query error:', error);
    res.status(500).json({
      error: 'Internal server error during query processing',
      message: error.message
    });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`\n🚀 AI Code Assistant Backend running on port ${PORT}`);
  console.log(`📁 Ingest files endpoint: POST http://localhost:${PORT}/ingest-files`);
  console.log(`❓ Query endpoint: POST http://localhost:${PORT}/query`);
  console.log(`📊 Status endpoint: GET http://localhost:${PORT}/status`);
  console.log(`\nSupported file extensions: ${SUPPORTED_EXTENSIONS.join(', ')}`);
  console.log(`Max tokens per chunk: 1000`);
  console.log(`AI Provider: ${process.env.HUGGINGFACE_API_KEY ? 'Hugging Face' : 'OpenAI'}\n`);
});
