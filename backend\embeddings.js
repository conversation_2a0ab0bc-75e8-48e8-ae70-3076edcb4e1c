const OpenAI = require('openai');

/**
 * OpenAI client instance
 */
let openai = null;

/**
 * Hugging Face API configuration
 */
const HF_API_URL = 'https://api-inference.huggingface.co/pipeline/feature-extraction/sentence-transformers/all-MiniLM-L6-v2';
const HF_TEXT_API_URL = 'https://api-inference.huggingface.co/models/microsoft/DialoGPT-medium';

/**
 * Initialize OpenAI client
 * @param {string} apiKey - OpenAI API key
 */
function initializeOpenAI(apiKey) {
  if (!apiKey) {
    throw new Error('OpenAI API key is required');
  }
  
  openai = new OpenAI({
    apiKey: apiKey
  });
}

/**
 * Create mock embedding vector for testing
 * @param {string} text - Text content to embed
 * @returns {Array} Mock embedding vector
 */
function createMockEmbedding(text) {
  // Create a deterministic mock embedding based on text content
  const embedding = [];
  const textHash = text.split('').reduce((hash, char) => {
    return ((hash << 5) - hash + char.charCodeAt(0)) & 0xffffffff;
  }, 0);

  // Generate 1536 dimensional vector (same as text-embedding-ada-002)
  for (let i = 0; i < 1536; i++) {
    const value = Math.sin(textHash + i) * 0.5;
    embedding.push(value);
  }

  return embedding;
}

/**
 * Create embeddings using Hugging Face API
 * @param {string} text - Text content to embed
 * @returns {Promise<Array>} Embedding vector
 */
async function createHuggingFaceEmbedding(text) {
  try {
    const response = await fetch(HF_API_URL, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.HUGGINGFACE_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        inputs: text.substring(0, 500) // Limit text length for HF API
      })
    });

    if (!response.ok) {
      throw new Error(`Hugging Face API error: ${response.status}`);
    }

    const embedding = await response.json();
    return Array.isArray(embedding) ? embedding : embedding[0];
  } catch (error) {
    console.error('Hugging Face embedding error:', error);
    throw error;
  }
}

/**
 * Generate text response using Hugging Face API
 * @param {string} question - User question
 * @param {Array} contextChunks - Relevant code chunks for context
 * @returns {Promise<string>} AI-generated response
 */
async function generateHuggingFaceResponse(question, contextChunks) {
  try {
    const context = contextChunks.map(chunk =>
      `File: ${chunk.path}\n${chunk.content.substring(0, 200)}...`
    ).join('\n\n');

    const prompt = `Based on this code context:\n${context}\n\nQuestion: ${question}\nAnswer:`;

    const response = await fetch(HF_TEXT_API_URL, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.HUGGINGFACE_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        inputs: prompt.substring(0, 1000),
        parameters: {
          max_length: 200,
          temperature: 0.7
        }
      })
    });

    if (!response.ok) {
      throw new Error(`Hugging Face text API error: ${response.status}`);
    }

    const result = await response.json();
    return result[0]?.generated_text || 'Sorry, I could not generate a response.';
  } catch (error) {
    console.error('Hugging Face text generation error:', error);
    return `Based on your code files (${contextChunks.map(c => c.path).join(', ')}), I can see this relates to your question: "${question}". However, I encountered an issue generating a detailed response. Please try rephrasing your question.`;
  }
}

/**
 * Create embeddings for text content using OpenAI's text-embedding-ada-002 model
 * @param {string} text - Text content to embed
 * @returns {Promise<Array>} Embedding vector
 */
async function createEmbedding(text) {
  // Check if mock mode is enabled
  if (process.env.MOCK_MODE === 'true') {
    console.log('🎭 Mock mode: Creating mock embedding');
    return createMockEmbedding(text);
  }

  // Try Hugging Face first if API key is available
  if (process.env.HUGGINGFACE_API_KEY) {
    console.log('🤗 Using Hugging Face for embeddings');
    return await createHuggingFaceEmbedding(text);
  }

  if (!openai) {
    throw new Error('OpenAI client not initialized. Call initializeOpenAI() first.');
  }

  if (!text || typeof text !== 'string') {
    throw new Error('Text content is required and must be a string');
  }

  try {
    const response = await openai.embeddings.create({
      model: 'text-embedding-ada-002',
      input: text.trim()
    });

    if (!response.data || !response.data[0] || !response.data[0].embedding) {
      throw new Error('Invalid response from OpenAI embeddings API');
    }

    return response.data[0].embedding;
  } catch (error) {
    if (error.response) {
      throw new Error(`OpenAI API error: ${error.response.status} - ${error.response.data?.error?.message || 'Unknown error'}`);
    } else if (error.request) {
      throw new Error('Network error: Unable to reach OpenAI API');
    } else {
      throw new Error(`Embedding creation failed: ${error.message}`);
    }
  }
}

/**
 * Create embeddings for multiple text chunks with rate limiting
 * @param {Array} chunks - Array of chunk objects with content
 * @param {Function} progressCallback - Optional callback for progress updates
 * @returns {Promise<Array>} Array of chunks with embeddings
 */
async function createEmbeddingsForChunks(chunks, progressCallback = null) {
  if (!Array.isArray(chunks)) {
    throw new Error('Chunks must be an array');
  }

  const chunksWithEmbeddings = [];
  const total = chunks.length;
  
  console.log(`Creating embeddings for ${total} chunks...`);

  for (let i = 0; i < chunks.length; i++) {
    const chunk = chunks[i];
    
    try {
      const embedding = await createEmbedding(chunk.content);
      
      chunksWithEmbeddings.push({
        ...chunk,
        embedding: embedding,
        embeddingCreatedAt: new Date().toISOString()
      });

      if (progressCallback) {
        progressCallback(i + 1, total);
      }

      // Add a small delay to respect rate limits
      if (i < chunks.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }

    } catch (error) {
      console.error(`Failed to create embedding for chunk ${i} (${chunk.path}):`, error.message);
      // Continue with other chunks even if one fails
      chunksWithEmbeddings.push({
        ...chunk,
        embedding: null,
        embeddingError: error.message,
        embeddingCreatedAt: new Date().toISOString()
      });
    }
  }

  const successCount = chunksWithEmbeddings.filter(chunk => chunk.embedding !== null).length;
  console.log(`Successfully created embeddings for ${successCount}/${total} chunks`);

  return chunksWithEmbeddings;
}

/**
 * Generate mock AI response for testing
 * @param {string} question - User question
 * @param {Array} contextChunks - Relevant code chunks for context
 * @returns {string} Mock AI response
 */
function generateMockAIResponse(question, contextChunks) {
  const fileNames = contextChunks.map(chunk => chunk.path).join(', ');

  return `🎭 **Mock AI Response**

**Your Question:** ${question}

**Mock Analysis:** Based on the code files I found (${fileNames}), here's a simulated response:

This appears to be related to your codebase structure. The folder selection feature you implemented allows users to:

1. **Select Project Folders** - Users can browse and select entire project directories
2. **Process Files Automatically** - The system reads supported file types (.js, .jsx, .ts, .tsx, .html, .css, .php)
3. **Create Embeddings** - File contents are processed and embedded for AI analysis
4. **Enable AI Chat** - Once processed, users can ask questions about their code

**Referenced Files:** ${contextChunks.length} code chunks from ${new Set(contextChunks.map(c => c.path)).size} files

*Note: This is a mock response for testing. Enable a valid OpenAI API key for real AI analysis.*`;
}

/**
 * Generate AI response using GPT-4o with context chunks
 * @param {string} question - User question
 * @param {Array} contextChunks - Relevant code chunks for context
 * @returns {Promise<string>} AI-generated response
 */
async function generateAIResponse(question, contextChunks) {
  // Check if mock mode is enabled
  if (process.env.MOCK_MODE === 'true') {
    console.log('🎭 Mock mode: Generating mock AI response');
    return generateMockAIResponse(question, contextChunks);
  }

  // Try Hugging Face first if API key is available
  if (process.env.HUGGINGFACE_API_KEY) {
    console.log('🤗 Using Hugging Face for text generation');
    return await generateHuggingFaceResponse(question, contextChunks);
  }

  if (!openai) {
    throw new Error('OpenAI client not initialized. Call initializeOpenAI() first.');
  }

  if (!question || typeof question !== 'string') {
    throw new Error('Question is required and must be a string');
  }

  if (!Array.isArray(contextChunks)) {
    throw new Error('Context chunks must be an array');
  }

  try {
    // Prepare context from code chunks
    const contextText = contextChunks.map((chunk) => {
      const chunkInfo = chunk.totalChunks > 1 
        ? ` (chunk ${chunk.chunkIndex + 1}/${chunk.totalChunks})`
        : '';
      
      return `File: ${chunk.path}${chunkInfo}\n\`\`\`\n${chunk.content}\n\`\`\`\n`;
    }).join('\n');

    const prompt = `You are an AI code assistant. Based on the following code context, answer the user's question.

Context:
${contextText}

Question: ${question}

Please provide a detailed and helpful answer based on the code provided above. Reference specific files and code sections when relevant.`;

    const response = await openai.chat.completions.create({
      model: 'gpt-4o',
      messages: [
        { role: 'user', content: prompt }
      ],
      max_tokens: 2000,
      temperature: 0.1
    });

    if (!response.choices || !response.choices[0] || !response.choices[0].message) {
      throw new Error('Invalid response from OpenAI chat API');
    }

    return response.choices[0].message.content.trim();
  } catch (error) {
    if (error.response) {
      throw new Error(`OpenAI API error: ${error.response.status} - ${error.response.data?.error?.message || 'Unknown error'}`);
    } else if (error.request) {
      throw new Error('Network error: Unable to reach OpenAI API');
    } else {
      throw new Error(`AI response generation failed: ${error.message}`);
    }
  }
}

module.exports = {
  initializeOpenAI,
  createEmbedding,
  createEmbeddingsForChunks,
  generateAIResponse
};
